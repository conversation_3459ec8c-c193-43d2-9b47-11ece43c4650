{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python and package management
    python311
    python311Packages.pip
    python311Packages.setuptools
    python311Packages.wheel
    
    # Core dependencies
    python311Packages.fastapi
    python311Packages.uvicorn
    python311Packages.sqlalchemy
    python311Packages.alembic
    python311Packages.asyncpg
    python311Packages.pydantic
    python311Packages.pydantic-settings
    python311Packages.python-jose
    python311Packages.passlib
    python311Packages.python-multipart
    python311Packages.httpx
    python311Packages.tenacity
    python311Packages.structlog
    python311Packages.rich
    
    # Development tools
    python311Packages.ruff
    python311Packages.mypy
    python311Packages.pytest
    python311Packages.pytest-asyncio
    python311Packages.pytest-cov
    # python311Packages.pre-commit  # Not available in this nixpkgs version
    
    # Database
    postgresql
    
    # Other tools
    git
    curl
  ];

  shellHook = ''
    echo "CVE Feed Service Development Environment"
    echo "Python version: $(python --version)"
    echo "Available commands:"
    echo "  - alembic: Database migrations"
    echo "  - uvicorn: ASGI server"
    echo "  - ruff: Linting and formatting"
    echo "  - mypy: Type checking"
    echo "  - pytest: Testing"
    
    # Set up environment variables
    export PYTHONPATH="$PWD/src:$PYTHONPATH"
    export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/cve_feed_dev"
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
      cat > .env << EOF
# Development environment settings
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/cve_feed_dev
SECRET_KEY=dev-secret-key-change-in-production
NVD_API_KEY=
LOG_LEVEL=INFO
EOF
      echo "Created .env file with default development settings"
    fi
  '';
}
