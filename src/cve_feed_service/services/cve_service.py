"""CVE service for business logic."""

from datetime import datetime
from typing import List, Optional, Tuple
from uuid import UUID

import structlog
from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.application import Application, Component, CPEMapping
from ..models.cve import CVE, CVECPEApplicability

logger = structlog.get_logger(__name__)


class CVEService:
    """Service for managing CVEs and vulnerability feeds."""

    def __init__(self, db: AsyncSession) -> None:
        """Initialize the service."""
        self.db = db

    async def get_cve_by_id(self, cve_id: str) -> Optional[CVE]:
        """Get CVE by ID."""
        query = select(CVE).where(
            and_(
                CVE.cve_id == cve_id,
                CVE.deleted_at.is_(None),
            )
        ).options(selectinload(CVE.cpe_applicability))
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def list_cves(
        self,
        severity: Optional[str] = None,
        published_after: Optional[str] = None,
        published_before: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Tuple[List[CVE], int]:
        """List CVEs with filtering."""
        query = select(CVE).where(CVE.deleted_at.is_(None))
        count_query = select(func.count(CVE.id)).where(CVE.deleted_at.is_(None))
        
        # Apply filters
        if severity:
            severity_filter = CVE.cvss_v3_severity == severity
            query = query.where(severity_filter)
            count_query = count_query.where(severity_filter)
        
        if published_after:
            try:
                after_date = datetime.fromisoformat(published_after.replace('Z', '+00:00'))
                date_filter = CVE.published_date >= after_date
                query = query.where(date_filter)
                count_query = count_query.where(date_filter)
            except ValueError:
                raise ValueError(f"Invalid date format: {published_after}")
        
        if published_before:
            try:
                before_date = datetime.fromisoformat(published_before.replace('Z', '+00:00'))
                date_filter = CVE.published_date <= before_date
                query = query.where(date_filter)
                count_query = count_query.where(date_filter)
            except ValueError:
                raise ValueError(f"Invalid date format: {published_before}")
        
        # Get total count
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # Get paginated results
        query = (
            query.offset(offset)
            .limit(limit)
            .order_by(CVE.published_date.desc().nulls_last())
        )
        
        result = await self.db.execute(query)
        cves = list(result.scalars().all())
        
        return cves, total

    async def get_tailored_feed(
        self,
        application_id: Optional[UUID] = None,
        severity: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Tuple[List[CVE], int]:
        """Get tailored CVE feed based on application components."""
        logger.info(
            "Getting tailored CVE feed",
            application_id=application_id,
            severity=severity,
            limit=limit,
            offset=offset,
        )
        
        if application_id:
            # Get CVEs that match the application's components
            return await self._get_application_cve_feed(
                application_id, severity, limit, offset
            )
        else:
            # Return general CVE feed
            return await self.list_cves(
                severity=severity,
                limit=limit,
                offset=offset,
            )

    async def _get_application_cve_feed(
        self,
        application_id: UUID,
        severity: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> Tuple[List[CVE], int]:
        """Get CVE feed tailored to a specific application."""
        # First, verify the application exists
        app_result = await self.db.execute(
            select(Application).where(
                and_(
                    Application.id == application_id,
                    Application.deleted_at.is_(None),
                )
            )
        )
        if not app_result.scalar_one_or_none():
            raise ValueError(f"Application with ID {application_id} not found")
        
        # Get all CPE strings for the application's components
        cpe_query = (
            select(CPEMapping.cpe_string)
            .join(Component, CPEMapping.component_id == Component.id)
            .where(
                and_(
                    Component.application_id == application_id,
                    Component.deleted_at.is_(None),
                    CPEMapping.deleted_at.is_(None),
                )
            )
        )
        
        cpe_result = await self.db.execute(cpe_query)
        cpe_strings = [row[0] for row in cpe_result.fetchall()]
        
        if not cpe_strings:
            logger.info("No CPE mappings found for application", application_id=application_id)
            return [], 0
        
        # Build CVE query that matches any of the CPE strings
        cve_query = (
            select(CVE)
            .join(CVECPEApplicability, CVE.cve_id == CVECPEApplicability.cve_id)
            .where(
                and_(
                    CVE.deleted_at.is_(None),
                    CVECPEApplicability.deleted_at.is_(None),
                    CVECPEApplicability.vulnerable == True,
                    or_(*[
                        CVECPEApplicability.cpe_string.like(f"{cpe}%")
                        for cpe in cpe_strings
                    ])
                )
            )
        )
        
        count_query = (
            select(func.count(func.distinct(CVE.id)))
            .join(CVECPEApplicability, CVE.cve_id == CVECPEApplicability.cve_id)
            .where(
                and_(
                    CVE.deleted_at.is_(None),
                    CVECPEApplicability.deleted_at.is_(None),
                    CVECPEApplicability.vulnerable == True,
                    or_(*[
                        CVECPEApplicability.cpe_string.like(f"{cpe}%")
                        for cpe in cpe_strings
                    ])
                )
            )
        )
        
        # Apply severity filter if specified
        if severity:
            severity_filter = CVE.cvss_v3_severity == severity
            cve_query = cve_query.where(severity_filter)
            count_query = count_query.where(severity_filter)
        
        # Get total count
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # Get paginated results with distinct CVEs
        cve_query = (
            cve_query.distinct()
            .offset(offset)
            .limit(limit)
            .order_by(CVE.published_date.desc().nulls_last())
        )
        
        result = await self.db.execute(cve_query)
        cves = list(result.scalars().all())
        
        logger.info(
            "Retrieved tailored CVE feed",
            application_id=application_id,
            cve_count=len(cves),
            total=total,
        )
        
        return cves, total
