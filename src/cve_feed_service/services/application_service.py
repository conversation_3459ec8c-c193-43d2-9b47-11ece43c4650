"""Application service for business logic."""

from typing import List, Optional
from uuid import U<PERSON><PERSON>

import structlog
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.application import Application
from ..schemas.application import ApplicationCreate, ApplicationUpdate

logger = structlog.get_logger(__name__)


class ApplicationService:
    """Service for managing applications."""

    def __init__(self, db: AsyncSession) -> None:
        """Initialize the service."""
        self.db = db

    async def create_application(self, application_data: ApplicationCreate) -> Application:
        """Create a new application."""
        logger.info("Creating application", name=application_data.name)
        
        # Check for existing application with same name and environment
        existing = await self.db.execute(
            select(Application).where(
                and_(
                    Application.name == application_data.name,
                    Application.environment == application_data.environment,
                    Application.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(
                f"Application '{application_data.name}' already exists in environment '{application_data.environment}'"
            )
        
        # Create new application
        application = Application(**application_data.model_dump())
        self.db.add(application)
        await self.db.commit()
        await self.db.refresh(application)
        
        return application

    async def get_application(
        self, 
        application_id: UUID, 
        include_components: bool = False
    ) -> Optional[Application]:
        """Get application by ID."""
        query = select(Application).where(
            and_(
                Application.id == application_id,
                Application.deleted_at.is_(None),
            )
        )
        
        if include_components:
            query = query.options(
                selectinload(Application.components).selectinload(
                    Application.components.property.mapper.class_.cpe_mappings
                )
            )
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def list_applications(
        self,
        skip: int = 0,
        limit: int = 100,
        environment: Optional[str] = None,
        criticality: Optional[str] = None,
    ) -> List[Application]:
        """List applications with optional filtering."""
        query = select(Application).where(Application.deleted_at.is_(None))
        
        if environment:
            query = query.where(Application.environment == environment)
        if criticality:
            query = query.where(Application.criticality == criticality)
        
        query = query.offset(skip).limit(limit).order_by(Application.created_at.desc())
        
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def update_application(
        self, 
        application_id: UUID, 
        application_data: ApplicationUpdate
    ) -> Optional[Application]:
        """Update an application."""
        application = await self.get_application(application_id)
        if not application:
            return None
        
        # Check for name/environment conflicts if updating those fields
        update_data = application_data.model_dump(exclude_unset=True)
        if "name" in update_data or "environment" in update_data:
            new_name = update_data.get("name", application.name)
            new_environment = update_data.get("environment", application.environment)
            
            existing = await self.db.execute(
                select(Application).where(
                    and_(
                        Application.name == new_name,
                        Application.environment == new_environment,
                        Application.id != application_id,
                        Application.deleted_at.is_(None),
                    )
                )
            )
            if existing.scalar_one_or_none():
                raise ValueError(
                    f"Application '{new_name}' already exists in environment '{new_environment}'"
                )
        
        # Update application
        for field, value in update_data.items():
            setattr(application, field, value)
        
        await self.db.commit()
        await self.db.refresh(application)
        
        return application

    async def delete_application(self, application_id: UUID) -> bool:
        """Soft delete an application."""
        application = await self.get_application(application_id, include_components=True)
        if not application:
            return False
        
        # Soft delete the application and all its components
        application.soft_delete()
        
        # Soft delete all components and their CPE mappings
        for component in application.components:
            if not component.is_deleted:
                component.soft_delete()
                for cpe_mapping in component.cpe_mappings:
                    if not cpe_mapping.is_deleted:
                        cpe_mapping.soft_delete()
        
        await self.db.commit()
        logger.info("Application soft deleted", application_id=application_id)
        
        return True
