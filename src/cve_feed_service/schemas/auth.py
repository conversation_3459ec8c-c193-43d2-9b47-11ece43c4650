"""Authentication and user schemas."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field

from ..models.user import UserRole


class UserBase(BaseModel):
    """Base user schema."""
    
    username: str = Field(..., min_length=3, max_length=100, description="Username")
    email: EmailStr = Field(..., description="Email address")
    full_name: Optional[str] = Field(None, max_length=255, description="Full name")
    role: UserRole = Field(..., description="User role")


class UserCreate(UserBase):
    """Schema for creating a user."""
    
    password: str = Field(..., min_length=8, description="Password")


class UserUpdate(BaseModel):
    """Schema for updating a user."""
    
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=255)
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """Schema for user responses."""
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class LoginRequest(BaseModel):
    """Schema for login requests."""
    
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")


class TokenResponse(BaseModel):
    """Schema for token responses."""
    
    access_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class APIKeyBase(BaseModel):
    """Base API key schema."""
    
    name: str = Field(..., min_length=1, max_length=100, description="API key name")


class APIKeyCreate(APIKeyBase):
    """Schema for creating an API key."""
    pass


class APIKeyResponse(APIKeyBase):
    """Schema for API key responses."""
    
    id: UUID
    user_id: UUID
    is_active: bool
    last_used_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class APIKeyCreateResponse(APIKeyResponse):
    """Schema for API key creation response (includes the actual key)."""
    
    api_key: str = Field(..., description="The actual API key (only shown once)")


class UserWithAPIKeysResponse(UserResponse):
    """User response with API keys."""
    
    api_keys: List[APIKeyResponse] = []


class PasswordChangeRequest(BaseModel):
    """Schema for password change requests."""
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
