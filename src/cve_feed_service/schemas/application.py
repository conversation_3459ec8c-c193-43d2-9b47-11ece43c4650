"""Pydantic schemas for application-related models."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ApplicationBase(BaseModel):
    """Base application schema."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Application name")
    description: Optional[str] = Field(None, description="Application description")
    version: Optional[str] = Field(None, max_length=100, description="Application version")
    owner: Optional[str] = Field(None, max_length=255, description="Application owner")
    environment: Optional[str] = Field(None, max_length=50, description="Environment (dev, staging, prod)")
    criticality: Optional[str] = Field(None, max_length=20, description="Business criticality level")


class ApplicationCreate(ApplicationBase):
    """Schema for creating an application."""
    pass


class ApplicationUpdate(BaseModel):
    """Schema for updating an application."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    version: Optional[str] = Field(None, max_length=100)
    owner: Optional[str] = Field(None, max_length=255)
    environment: Optional[str] = Field(None, max_length=50)
    criticality: Optional[str] = Field(None, max_length=20)


class ApplicationResponse(ApplicationBase):
    """Schema for application responses."""
    
    id: UUID
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ComponentBase(BaseModel):
    """Base component schema."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Component name")
    version: Optional[str] = Field(None, max_length=100, description="Component version")
    vendor: Optional[str] = Field(None, max_length=255, description="Component vendor")
    component_type: Optional[str] = Field(None, max_length=50, description="Component type")
    description: Optional[str] = Field(None, description="Component description")


class ComponentCreate(ComponentBase):
    """Schema for creating a component."""
    pass


class ComponentUpdate(BaseModel):
    """Schema for updating a component."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    version: Optional[str] = Field(None, max_length=100)
    vendor: Optional[str] = Field(None, max_length=255)
    component_type: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None


class ComponentResponse(ComponentBase):
    """Schema for component responses."""
    
    id: UUID
    application_id: UUID
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CPEMappingBase(BaseModel):
    """Base CPE mapping schema."""
    
    cpe_string: str = Field(..., min_length=1, max_length=500, description="CPE 2.3 string")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Mapping confidence score")
    mapping_source: Optional[str] = Field(None, max_length=50, description="Source of the mapping")


class CPEMappingCreate(CPEMappingBase):
    """Schema for creating a CPE mapping."""
    pass


class CPEMappingUpdate(BaseModel):
    """Schema for updating a CPE mapping."""
    
    cpe_string: Optional[str] = Field(None, min_length=1, max_length=500)
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    mapping_source: Optional[str] = Field(None, max_length=50)


class CPEMappingResponse(CPEMappingBase):
    """Schema for CPE mapping responses."""
    
    id: UUID
    component_id: UUID
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Extended response schemas with relationships
class ComponentWithCPEResponse(ComponentResponse):
    """Component response with CPE mappings."""
    
    cpe_mappings: List[CPEMappingResponse] = []


class ApplicationWithComponentsResponse(ApplicationResponse):
    """Application response with components."""
    
    components: List[ComponentWithCPEResponse] = []
