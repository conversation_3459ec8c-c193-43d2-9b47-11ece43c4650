"""Pydantic schemas for CVE-related models."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class CVECPEApplicabilityResponse(BaseModel):
    """Schema for CVE CPE applicability responses."""
    
    id: UUID
    cve_id: str
    cpe_string: str
    version_start_including: Optional[str] = None
    version_start_excluding: Optional[str] = None
    version_end_including: Optional[str] = None
    version_end_excluding: Optional[str] = None
    vulnerable: bool
    configuration_id: Optional[str] = None
    source: str
    confidence: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CVEResponse(BaseModel):
    """Schema for CVE responses."""
    
    id: UUID
    cve_id: str
    description: Optional[str] = None
    published_date: Optional[datetime] = None
    last_modified_date: Optional[datetime] = None
    cvss_v3_score: Optional[float] = None
    cvss_v3_vector: Optional[str] = None
    cvss_v3_severity: Optional[str] = None
    cvss_v2_score: Optional[float] = None
    cvss_v2_vector: Optional[str] = None
    cwe_ids: Optional[List[str]] = None
    references: Optional[List[Dict[str, Any]]] = None
    vendor_advisories: Optional[List[Dict[str, Any]]] = None
    nvd_last_modified: Optional[datetime] = None
    source: str
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CVEWithApplicabilityResponse(CVEResponse):
    """CVE response with CPE applicability."""
    
    cpe_applicability: List[CVECPEApplicabilityResponse] = []


class CVEFeedQuery(BaseModel):
    """Schema for CVE feed query parameters."""
    
    application_id: Optional[UUID] = Field(None, description="Filter by application ID")
    severity: Optional[str] = Field(None, description="Filter by CVSS severity (LOW, MEDIUM, HIGH, CRITICAL)")
    limit: int = Field(100, ge=1, le=1000, description="Maximum number of results")
    offset: int = Field(0, ge=0, description="Pagination offset")
    published_after: Optional[datetime] = Field(None, description="Filter CVEs published after this date")
    published_before: Optional[datetime] = Field(None, description="Filter CVEs published before this date")


class CVEFeedResponse(BaseModel):
    """Schema for CVE feed responses."""
    
    cves: List[CVEResponse]
    total: int
    limit: int
    offset: int
    has_more: bool
