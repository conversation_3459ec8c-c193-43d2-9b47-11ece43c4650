"""Database base configuration and utilities."""

from datetime import datetime
from typing import Any, Optional
from uuid import UUID, uuid4

from sqlalchemy import DateTime, String, func
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    """Base class for all database models."""

    pass


class SoftDeleteMixin:
    """Mixin to add soft delete functionality to models."""

    @declared_attr
    def deleted_at(cls) -> Mapped[Optional[datetime]]:
        """Timestamp when the record was soft-deleted."""
        return mapped_column(DateTime(timezone=True), nullable=True, index=True)

    def soft_delete(self) -> None:
        """Mark the record as deleted."""
        self.deleted_at = func.now()

    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None

    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None


class TimestampMixin:
    """Mixin to add timestamp fields to models."""

    @declared_attr
    def created_at(cls) -> Mapped[datetime]:
        """Timestamp when the record was created."""
        return mapped_column(
            DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
            index=True,
        )

    @declared_attr
    def updated_at(cls) -> Mapped[datetime]:
        """Timestamp when the record was last updated."""
        return mapped_column(
            DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
            onupdate=func.now(),
            index=True,
        )


class UUIDMixin:
    """Mixin to add UUID primary key to models."""

    @declared_attr
    def id(cls) -> Mapped[UUID]:
        """Primary key UUID."""
        return mapped_column(
            PostgresUUID(as_uuid=True),
            primary_key=True,
            default=uuid4,
            nullable=False,
        )


class BaseModel(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin):
    """Base model with UUID, timestamps, and soft delete."""

    __abstract__ = True

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"

    def to_dict(self) -> dict[str, Any]:
        """Convert model to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
