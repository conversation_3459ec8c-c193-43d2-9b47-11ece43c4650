"""CVE and related models."""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import DateTime, Float, Index, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..db.base import BaseModel


class CVE(BaseModel):
    """CVE model for vulnerability data."""

    __tablename__ = "cves"

    cve_id: Mapped[str] = mapped_column(String(20), nullable=False, unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    published_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    last_modified_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    
    # CVSS Scores
    cvss_v3_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True, index=True)
    cvss_v3_vector: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    cvss_v3_severity: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, index=True)
    cvss_v2_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    cvss_v2_vector: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # CWE Information
    cwe_ids: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True)
    
    # References and metadata
    references: Mapped[Optional[List[dict]]] = mapped_column(JSONB, nullable=True)
    vendor_advisories: Mapped[Optional[List[dict]]] = mapped_column(JSONB, nullable=True)
    
    # NVD specific fields
    nvd_last_modified: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    source: Mapped[str] = mapped_column(String(50), nullable=False, default="NVD")
    
    # Raw data for debugging/analysis
    raw_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)

    # Relationships
    cpe_applicability: Mapped[List["CVECPEApplicability"]] = relationship(
        "CVECPEApplicability",
        back_populates="cve",
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraint for active records only
        UniqueConstraint(
            "cve_id",
            name="uq_cve_id_active",
        ),
        Index(
            "ix_cve_published_date_active",
            "published_date",
        ),
        Index(
            "ix_cve_severity_active",
            "cvss_v3_severity",
        ),
        Index(
            "ix_cve_score_active",
            "cvss_v3_score",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the CVE."""
        return f"<CVE(id={self.id}, cve_id='{self.cve_id}', severity='{self.cvss_v3_severity}')>"

    @property
    def severity_score(self) -> Optional[float]:
        """Get the best available CVSS score."""
        return self.cvss_v3_score or self.cvss_v2_score

    @property
    def severity_level(self) -> Optional[str]:
        """Get the severity level."""
        return self.cvss_v3_severity


class CVECPEApplicability(BaseModel):
    """Model for CVE to CPE applicability mapping."""

    __tablename__ = "cve_cpe_applicability"

    cve_id: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
    )
    cpe_string: Mapped[str] = mapped_column(String(500), nullable=False, index=True)
    
    # Version range information
    version_start_including: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    version_start_excluding: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    version_end_including: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    version_end_excluding: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Configuration and matching metadata
    vulnerable: Mapped[bool] = mapped_column(nullable=False, default=True, index=True)
    configuration_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Source and confidence
    source: Mapped[str] = mapped_column(String(50), nullable=False, default="NVD")
    confidence: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    # Relationships
    cve: Mapped["CVE"] = relationship(
        "CVE",
        back_populates="cpe_applicability",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraint for active records only
        UniqueConstraint(
            "cve_id",
            "cpe_string",
            "configuration_id",
            name="uq_cve_cpe_config_active",
        ),
        Index(
            "ix_cve_cpe_cve_id_active",
            "cve_id",
        ),
        Index(
            "ix_cve_cpe_string_active",
            "cpe_string",
        ),
        Index(
            "ix_cve_cpe_vulnerable_active",
            "vulnerable",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the CVE CPE applicability."""
        return f"<CVECPEApplicability(cve_id='{self.cve_id}', cpe='{self.cpe_string}')>"
