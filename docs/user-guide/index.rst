User Guide
==========

This comprehensive user guide covers all aspects of using the CVE Feed Service, from basic setup to advanced vulnerability management workflows.

.. toctree::
   :maxdepth: 2

   authentication
   application-management
   component-management
   cpe-mapping
   vulnerability-feeds
   data-management
   workflows

Overview
--------

The CVE Feed Service helps organizations manage their vulnerability exposure by providing tailored CVE feeds based on actual application inventories. This guide will walk you through:

1. **Authentication**: Setting up users and managing access
2. **Application Management**: Registering and organizing your applications
3. **Component Management**: Tracking software components and dependencies
4. **CPE Mapping**: Linking components to vulnerability identifiers
5. **Vulnerability Feeds**: Consuming tailored CVE information
6. **Data Management**: Importing and maintaining CVE data
7. **Workflows**: Common use cases and best practices

Getting Started Checklist
--------------------------

Before diving into the detailed guides, ensure you have:

☐ **System Access**
   - CVE Feed Service installed and running
   - Database properly configured
   - Network access to NVD API

☐ **Authentication Setup**
   - Initial admin user created
   - User roles understood
   - API access configured

☐ **Initial Data**
   - CVE data imported from NVD
   - At least one application registered
   - Basic understanding of CPE format

☐ **API Access**
   - API documentation accessible at ``/api/v1/docs``
   - Authentication token or API key obtained
   - HTTP client configured (curl, Postman, etc.)

Quick Start Workflow
--------------------

Here's a typical workflow to get your first tailored vulnerability feed:

1. **Authenticate** and obtain an access token
2. **Create an application** representing one of your systems
3. **Add components** that make up your application
4. **Map components to CPEs** for vulnerability correlation
5. **Request a tailored feed** to see relevant vulnerabilities

Example: Web Application Setup
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Let's walk through setting up a typical web application:

.. code-block:: bash

   # 1. Login and get token
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}'

   # Save the token from response
   export TOKEN="your-jwt-token-here"

   # 2. Create application
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "E-commerce Website",
          "environment": "production",
          "criticality": "high",
          "description": "Main customer-facing e-commerce platform"
        }'

   # Save the application ID from response
   export APP_ID="application-uuid-here"

   # 3. Add web server component
   curl -X POST "http://localhost:8000/api/v1/applications/$APP_ID/components" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server"
        }'

   # Save the component ID from response
   export COMPONENT_ID="component-uuid-here"

   # 4. Add CPE mapping
   curl -X POST "http://localhost:8000/api/v1/components/$COMPONENT_ID/cpe-mappings" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "manual"
        }'

   # 5. Get tailored vulnerability feed
   curl "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID&severity=HIGH" \
        -H "Authorization: Bearer $TOKEN"

This workflow demonstrates the core functionality and shows how applications, components, and CPE mappings work together to provide relevant vulnerability information.

Navigation Tips
---------------

**For New Users:**
   Start with :doc:`authentication` to understand access control, then follow the guides in order.

**For Administrators:**
   Focus on :doc:`authentication` and :doc:`data-management` for system setup and maintenance.

**For Security Analysts:**
   Jump to :doc:`vulnerability-feeds` and :doc:`workflows` for day-to-day operations.

**For Developers:**
   Review the API examples in each section and check the API reference documentation.

Common Use Cases
----------------

**Vulnerability Assessment**
   Regular review of vulnerabilities affecting your application portfolio

**Incident Response**
   Quickly identify affected systems when new critical vulnerabilities are disclosed

**Compliance Reporting**
   Generate reports showing vulnerability management coverage and response

**DevSecOps Integration**
   Integrate vulnerability feeds into CI/CD pipelines and security automation

**Risk Prioritization**
   Combine vulnerability severity with business criticality for better decision making

Support Resources
-----------------

* **Interactive API Documentation**: Available at ``/api/v1/docs`` when the service is running
* **GitHub Repository**: Source code, issues, and discussions
* **Example Scripts**: Sample code for common operations
* **Community**: User discussions and shared workflows

Next Steps
----------

Choose your path based on your role and immediate needs:

* **System Administrator**: Start with :doc:`authentication` and :doc:`data-management`
* **Security Analyst**: Begin with :doc:`application-management` and :doc:`vulnerability-feeds`
* **Developer**: Review :doc:`authentication` for API access, then explore specific endpoints
