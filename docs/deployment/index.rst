Deployment Guide
================

This guide covers deploying the CVE Feed Service in production environments, including containerization, scaling, monitoring, and maintenance procedures.

.. toctree::
   :maxdepth: 2

   docker
   kubernetes
   monitoring
   backup

Overview
--------

The CVE Feed Service is designed for production deployment with:

* **Containerized Architecture**: Docker support for consistent deployments
* **Horizontal Scaling**: Stateless design enables multiple instances
* **Health Monitoring**: Built-in health and readiness endpoints
* **Configuration Management**: Environment-based configuration
* **Security**: Production-ready authentication and authorization

Deployment Options
------------------

**Single Server Deployment**
   Simple deployment on a single server with Docker Compose

**Container Orchestration**
   Kubernetes deployment with auto-scaling and load balancing

**Cloud Platforms**
   Deployment on AWS, GCP, Azure with managed services

**Hybrid Deployment**
   On-premises with cloud backup and disaster recovery

Prerequisites
-------------

**System Requirements:**
* **CPU**: 2+ cores (4+ recommended for production)
* **Memory**: 4GB RAM minimum (8GB+ recommended)
* **Storage**: 50GB+ for CVE data (grows over time)
* **Network**: Internet access for NVD API integration

**Software Requirements:**
* **Container Runtime**: Docker 20.10+ or containerd
* **Database**: PostgreSQL 12+ (can be external)
* **Load Balancer**: nginx, HAProxy, or cloud load balancer
* **Monitoring**: Prometheus, Grafana (optional)

Docker Deployment
-----------------

Basic Docker Setup
~~~~~~~~~~~~~~~~~~

**1. Create Production Dockerfile:**

.. code-block:: dockerfile

   # Dockerfile.prod
   FROM python:3.11-slim

   # Set environment variables
   ENV PYTHONUNBUFFERED=1
   ENV PYTHONDONTWRITEBYTECODE=1

   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       postgresql-client \
       && rm -rf /var/lib/apt/lists/*

   # Create app user
   RUN useradd --create-home --shell /bin/bash app

   # Set work directory
   WORKDIR /app

   # Copy requirements and install Python dependencies
   COPY pyproject.toml .
   RUN pip install --no-cache-dir .

   # Copy application code
   COPY src/ src/
   COPY alembic/ alembic/
   COPY alembic.ini .

   # Change ownership to app user
   RUN chown -R app:app /app
   USER app

   # Expose port
   EXPOSE 8000

   # Health check
   HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
     CMD curl -f http://localhost:8000/health || exit 1

   # Run application
   CMD ["python", "-m", "src.cve_feed_service.main"]

**2. Docker Compose for Production:**

.. code-block:: yaml

   # docker-compose.prod.yml
   version: '3.8'

   services:
     db:
       image: postgres:15
       environment:
         POSTGRES_DB: cve_feed_prod
         POSTGRES_USER: cve_feed_user
         POSTGRES_PASSWORD: ${DB_PASSWORD}
       volumes:
         - postgres_data:/var/lib/postgresql/data
         - ./init.sql:/docker-entrypoint-initdb.d/init.sql
       ports:
         - "5432:5432"
       restart: unless-stopped

     app:
       build:
         context: .
         dockerfile: Dockerfile.prod
       environment:
         DATABASE_URL: postgresql+asyncpg://cve_feed_user:${DB_PASSWORD}@db:5432/cve_feed_prod
         SECRET_KEY: ${SECRET_KEY}
         NVD_API_KEY: ${NVD_API_KEY}
         ENVIRONMENT: production
         LOG_LEVEL: INFO
       ports:
         - "8000:8000"
       depends_on:
         - db
       restart: unless-stopped
       volumes:
         - ./logs:/app/logs

     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - app
       restart: unless-stopped

   volumes:
     postgres_data:

**3. Environment Configuration:**

.. code-block:: bash

   # .env.prod
   DB_PASSWORD=secure_database_password
   SECRET_KEY=your_very_secure_secret_key_here
   NVD_API_KEY=your_nvd_api_key
   ENVIRONMENT=production

**4. Nginx Configuration:**

.. code-block:: nginx

   # nginx.conf
   events {
       worker_connections 1024;
   }

   http {
       upstream app {
           server app:8000;
       }

       server {
           listen 80;
           server_name your-domain.com;
           return 301 https://$server_name$request_uri;
       }

       server {
           listen 443 ssl http2;
           server_name your-domain.com;

           ssl_certificate /etc/nginx/ssl/cert.pem;
           ssl_certificate_key /etc/nginx/ssl/key.pem;

           location / {
               proxy_pass http://app;
               proxy_set_header Host $host;
               proxy_set_header X-Real-IP $remote_addr;
               proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
               proxy_set_header X-Forwarded-Proto $scheme;
           }

           location /health {
               proxy_pass http://app/health;
               access_log off;
           }
       }
   }

**5. Deploy with Docker Compose:**

.. code-block:: bash

   # Build and start services
   docker-compose -f docker-compose.prod.yml up -d

   # Run database migrations
   docker-compose -f docker-compose.prod.yml exec app alembic upgrade head

   # Import initial CVE data
   docker-compose -f docker-compose.prod.yml exec app \
     python -m src.cve_feed_service.cli.main cve bulk-import --years 2

Kubernetes Deployment
---------------------

**1. Namespace and ConfigMap:**

.. code-block:: yaml

   # k8s/namespace.yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: cve-feed

   ---
   # k8s/configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: cve-feed-config
     namespace: cve-feed
   data:
     ENVIRONMENT: "production"
     LOG_LEVEL: "INFO"
     DATABASE_URL: "postgresql+asyncpg://cve_feed_user:$(DB_PASSWORD)@postgres:5432/cve_feed_prod"

**2. Secrets:**

.. code-block:: yaml

   # k8s/secrets.yaml
   apiVersion: v1
   kind: Secret
   metadata:
     name: cve-feed-secrets
     namespace: cve-feed
   type: Opaque
   data:
     DB_PASSWORD: <base64-encoded-password>
     SECRET_KEY: <base64-encoded-secret>
     NVD_API_KEY: <base64-encoded-api-key>

**3. PostgreSQL Deployment:**

.. code-block:: yaml

   # k8s/postgres.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: postgres
     namespace: cve-feed
   spec:
     replicas: 1
     selector:
       matchLabels:
         app: postgres
     template:
       metadata:
         labels:
           app: postgres
       spec:
         containers:
         - name: postgres
           image: postgres:15
           env:
           - name: POSTGRES_DB
             value: cve_feed_prod
           - name: POSTGRES_USER
             value: cve_feed_user
           - name: POSTGRES_PASSWORD
             valueFrom:
               secretKeyRef:
                 name: cve-feed-secrets
                 key: DB_PASSWORD
           ports:
           - containerPort: 5432
           volumeMounts:
           - name: postgres-storage
             mountPath: /var/lib/postgresql/data
         volumes:
         - name: postgres-storage
           persistentVolumeClaim:
             claimName: postgres-pvc

   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: postgres
     namespace: cve-feed
   spec:
     selector:
       app: postgres
     ports:
     - port: 5432
       targetPort: 5432

**4. Application Deployment:**

.. code-block:: yaml

   # k8s/app.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: cve-feed-app
     namespace: cve-feed
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: cve-feed-app
     template:
       metadata:
         labels:
           app: cve-feed-app
       spec:
         containers:
         - name: app
           image: cve-feed:latest
           ports:
           - containerPort: 8000
           env:
           - name: DATABASE_URL
             valueFrom:
               configMapKeyRef:
                 name: cve-feed-config
                 key: DATABASE_URL
           - name: SECRET_KEY
             valueFrom:
               secretKeyRef:
                 name: cve-feed-secrets
                 key: SECRET_KEY
           - name: NVD_API_KEY
             valueFrom:
               secretKeyRef:
                 name: cve-feed-secrets
                 key: NVD_API_KEY
           envFrom:
           - configMapRef:
               name: cve-feed-config
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /readiness
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 5
           resources:
             requests:
               memory: "512Mi"
               cpu: "250m"
             limits:
               memory: "1Gi"
               cpu: "500m"

   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: cve-feed-service
     namespace: cve-feed
   spec:
     selector:
       app: cve-feed-app
     ports:
     - port: 80
       targetPort: 8000
     type: ClusterIP

**5. Ingress Configuration:**

.. code-block:: yaml

   # k8s/ingress.yaml
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: cve-feed-ingress
     namespace: cve-feed
     annotations:
       kubernetes.io/ingress.class: nginx
       cert-manager.io/cluster-issuer: letsencrypt-prod
   spec:
     tls:
     - hosts:
       - cve-feed.your-domain.com
       secretName: cve-feed-tls
     rules:
     - host: cve-feed.your-domain.com
       http:
         paths:
         - path: /
           pathType: Prefix
           backend:
             service:
               name: cve-feed-service
               port:
                 number: 80

**6. Deploy to Kubernetes:**

.. code-block:: bash

   # Apply all configurations
   kubectl apply -f k8s/

   # Check deployment status
   kubectl get pods -n cve-feed

   # Run migrations
   kubectl exec -n cve-feed deployment/cve-feed-app -- alembic upgrade head

   # Import initial data
   kubectl exec -n cve-feed deployment/cve-feed-app -- \
     python -m src.cve_feed_service.cli.main cve bulk-import --years 2

Production Configuration
-----------------------

Security Configuration
~~~~~~~~~~~~~~~~~~~~~~

**1. Environment Variables:**

.. code-block:: bash

   # Production environment variables
   ENVIRONMENT=production
   DEBUG=false
   SECRET_KEY=your-very-secure-secret-key-minimum-32-characters
   DATABASE_URL=postgresql+asyncpg://user:pass@host:5432/db
   NVD_API_KEY=your-nvd-api-key
   
   # Security settings
   ALLOWED_HOSTS=your-domain.com,api.your-domain.com
   CORS_ORIGINS=https://your-frontend.com
   
   # Logging
   LOG_LEVEL=INFO
   LOG_FORMAT=json

**2. Database Security:**

.. code-block:: sql

   -- Create dedicated database user
   CREATE USER cve_feed_user WITH PASSWORD 'secure_password';
   CREATE DATABASE cve_feed_prod OWNER cve_feed_user;
   
   -- Grant minimal required permissions
   GRANT CONNECT ON DATABASE cve_feed_prod TO cve_feed_user;
   GRANT USAGE ON SCHEMA public TO cve_feed_user;
   GRANT CREATE ON SCHEMA public TO cve_feed_user;

**3. SSL/TLS Configuration:**

.. code-block:: bash

   # Generate SSL certificate (Let's Encrypt example)
   certbot certonly --webroot -w /var/www/html -d your-domain.com

   # Or use existing certificates
   cp your-cert.pem /etc/ssl/certs/
   cp your-key.pem /etc/ssl/private/

Performance Tuning
~~~~~~~~~~~~~~~~~~

**1. Database Optimization:**

.. code-block:: sql

   -- PostgreSQL configuration for production
   -- postgresql.conf
   shared_buffers = 256MB
   effective_cache_size = 1GB
   maintenance_work_mem = 64MB
   checkpoint_completion_target = 0.9
   wal_buffers = 16MB
   default_statistics_target = 100
   random_page_cost = 1.1
   effective_io_concurrency = 200

**2. Application Configuration:**

.. code-block:: bash

   # Application performance settings
   WORKERS=4  # Number of worker processes
   MAX_CONNECTIONS=100  # Database connection pool size
   REQUEST_TIMEOUT=30  # Request timeout in seconds
   
   # CVE import settings
   CVE_BATCH_SIZE=100  # CVEs processed per batch
   NVD_RATE_LIMIT_PER_MINUTE=50  # With API key

**3. Caching Configuration:**

.. code-block:: yaml

   # Redis for caching (optional)
   redis:
     image: redis:7-alpine
     ports:
       - "6379:6379"
     volumes:
       - redis_data:/data
     restart: unless-stopped

Monitoring and Logging
----------------------

Application Monitoring
~~~~~~~~~~~~~~~~~~~~~~

**1. Health Checks:**

.. code-block:: bash

   # Health endpoint
   curl https://your-domain.com/health

   # Readiness endpoint
   curl https://your-domain.com/readiness

**2. Prometheus Metrics:**

.. code-block:: python

   # Add to main.py for metrics collection
   from prometheus_fastapi_instrumentator import Instrumentator

   app = FastAPI(...)
   Instrumentator().instrument(app).expose(app)

**3. Grafana Dashboard:**

.. code-block:: yaml

   # docker-compose monitoring addition
   prometheus:
     image: prom/prometheus
     ports:
       - "9090:9090"
     volumes:
       - ./prometheus.yml:/etc/prometheus/prometheus.yml

   grafana:
     image: grafana/grafana
     ports:
       - "3000:3000"
     environment:
       - GF_SECURITY_ADMIN_PASSWORD=admin
     volumes:
       - grafana_data:/var/lib/grafana

Structured Logging
~~~~~~~~~~~~~~~~~~

**1. Log Configuration:**

.. code-block:: python

   # Structured logging setup
   import structlog

   structlog.configure(
       processors=[
           structlog.stdlib.filter_by_level,
           structlog.stdlib.add_logger_name,
           structlog.stdlib.add_log_level,
           structlog.stdlib.PositionalArgumentsFormatter(),
           structlog.processors.TimeStamper(fmt="iso"),
           structlog.processors.StackInfoRenderer(),
           structlog.processors.format_exc_info,
           structlog.processors.JSONRenderer(),
       ],
       context_class=dict,
       logger_factory=structlog.stdlib.LoggerFactory(),
       cache_logger_on_first_use=True,
   )

**2. Log Aggregation:**

.. code-block:: yaml

   # ELK Stack for log aggregation
   elasticsearch:
     image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
     environment:
       - discovery.type=single-node
       - xpack.security.enabled=false
     ports:
       - "9200:9200"

   logstash:
     image: docker.elastic.co/logstash/logstash:8.5.0
     volumes:
       - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

   kibana:
     image: docker.elastic.co/kibana/kibana:8.5.0
     ports:
       - "5601:5601"
     environment:
       - ELASTICSEARCH_HOSTS=http://elasticsearch:9200

Backup and Disaster Recovery
----------------------------

Database Backup
~~~~~~~~~~~~~~~

**1. Automated Backups:**

.. code-block:: bash

   #!/bin/bash
   # backup.sh - Daily database backup script

   BACKUP_DIR="/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   DB_NAME="cve_feed_prod"

   # Create backup
   pg_dump $DB_NAME | gzip > "$BACKUP_DIR/cve_feed_backup_$DATE.sql.gz"

   # Retain last 30 days
   find $BACKUP_DIR -name "cve_feed_backup_*.sql.gz" -mtime +30 -delete

   # Upload to cloud storage (optional)
   aws s3 cp "$BACKUP_DIR/cve_feed_backup_$DATE.sql.gz" s3://your-backup-bucket/

**2. Backup Verification:**

.. code-block:: bash

   # Test backup restoration
   gunzip -c cve_feed_backup_20250118.sql.gz | psql cve_feed_test

**3. Point-in-Time Recovery:**

.. code-block:: sql

   -- Enable WAL archiving for PITR
   -- postgresql.conf
   wal_level = replica
   archive_mode = on
   archive_command = 'cp %p /archive/%f'

Scaling Considerations
---------------------

Horizontal Scaling
~~~~~~~~~~~~~~~~~

**1. Load Balancing:**

.. code-block:: nginx

   # nginx load balancer configuration
   upstream cve_feed_backend {
       server app1:8000;
       server app2:8000;
       server app3:8000;
   }

   server {
       location / {
           proxy_pass http://cve_feed_backend;
       }
   }

**2. Database Scaling:**

.. code-block:: yaml

   # Read replicas for scaling reads
   postgres-primary:
     image: postgres:15
     environment:
       POSTGRES_REPLICATION_MODE: master
       POSTGRES_REPLICATION_USER: replicator
       POSTGRES_REPLICATION_PASSWORD: replicator_password

   postgres-replica:
     image: postgres:15
     environment:
       POSTGRES_REPLICATION_MODE: slave
       POSTGRES_MASTER_HOST: postgres-primary
       POSTGRES_REPLICATION_USER: replicator
       POSTGRES_REPLICATION_PASSWORD: replicator_password

**3. Auto-scaling (Kubernetes):**

.. code-block:: yaml

   # Horizontal Pod Autoscaler
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: cve-feed-hpa
     namespace: cve-feed
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: cve-feed-app
     minReplicas: 3
     maxReplicas: 10
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70

Maintenance Procedures
---------------------

Regular Maintenance
~~~~~~~~~~~~~~~~~~

**1. System Updates:**

.. code-block:: bash

   # Update application
   docker-compose pull
   docker-compose up -d

   # Update database schema
   docker-compose exec app alembic upgrade head

**2. Data Maintenance:**

.. code-block:: bash

   # Update CVE data
   docker-compose exec app python -m src.cve_feed_service.cli.main cve incremental-update

   # Database maintenance
   docker-compose exec db psql -U cve_feed_user -d cve_feed_prod -c "VACUUM ANALYZE;"

**3. Log Rotation:**

.. code-block:: bash

   # logrotate configuration
   /app/logs/*.log {
       daily
       rotate 30
       compress
       delaycompress
       missingok
       notifempty
       create 644 app app
   }

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**1. Database Connection Issues:**

.. code-block:: bash

   # Check database connectivity
   docker-compose exec app pg_isready -h db -p 5432

   # Check database logs
   docker-compose logs db

**2. Memory Issues:**

.. code-block:: bash

   # Monitor memory usage
   docker stats

   # Check application logs for memory errors
   docker-compose logs app | grep -i memory

**3. Performance Issues:**

.. code-block:: bash

   # Check slow queries
   docker-compose exec db psql -U cve_feed_user -d cve_feed_prod -c "
     SELECT query, mean_time, calls 
     FROM pg_stat_statements 
     ORDER BY mean_time DESC 
     LIMIT 10;
   "

**4. SSL Certificate Issues:**

.. code-block:: bash

   # Check certificate expiration
   openssl x509 -in /etc/ssl/certs/your-cert.pem -noout -dates

   # Renew Let's Encrypt certificate
   certbot renew
