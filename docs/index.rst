CVE Feed Service Documentation
=================================

Welcome to the CVE Feed Service documentation. This service provides tailored vulnerability feeds based on application component inventories, helping organizations focus on the vulnerabilities that actually affect their systems.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   overview
   installation
   user-guide/index
   api-reference/index
   development/index
   deployment/index

Quick Start
-----------

The CVE Feed Service is a FastAPI-based application that:

* Manages application inventories with component tracking
* Ingests CVE data from the National Vulnerability Database (NVD)
* Provides tailored vulnerability feeds based on CPE (Common Platform Enumeration) mappings
* Offers role-based access control for security teams

Key Features
------------

✅ **Application Inventory Management**
   Complete CRUD operations for applications and their components

✅ **CVE Data Ingestion**
   Automated ingestion from NVD with rate limiting and incremental updates

✅ **Tailored Vulnerability Feeds**
   Application-specific CVE feeds based on component CPE mappings

✅ **Authentication & Authorization**
   JWT-based auth with role-based access control (RBAC)

✅ **CPE Mapping & Validation**
   Support for CPE 2.3 format with validation and matching logic

✅ **Soft Delete Architecture**
   All data operations support soft deletion for audit trails

Getting Started
---------------

1. **Installation**: Follow the :doc:`installation` guide to set up your environment
2. **User Guide**: Read the :doc:`user-guide/index` for detailed usage instructions
3. **API Reference**: Explore the :doc:`api-reference/index` for technical details
4. **Development**: See :doc:`development/index` for contributing guidelines

Architecture Overview
---------------------

The CVE Feed Service follows a modern, API-first architecture:

.. code-block:: text

   ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
   │   Web UI        │    │   CLI Tools     │    │  External APIs  │
   │   (Future)      │    │                 │    │                 │
   └─────────────────┘    └─────────────────┘    └─────────────────┘
            │                       │                       │
            └───────────────────────┼───────────────────────┘
                                    │
   ┌─────────────────────────────────┼─────────────────────────────────┐
   │                    FastAPI Application                            │
   │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐ │
   │  │     API     │  │   Auth      │  │  Services   │  │   Utils   │ │
   │  │ Endpoints   │  │ & Security  │  │   Layer     │  │           │ │
   │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘ │
   └─────────────────────────────────┼─────────────────────────────────┘
                                     │
   ┌─────────────────────────────────┼─────────────────────────────────┐
   │                    Data Layer                                     │
   │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐ │
   │  │ PostgreSQL  │  │ SQLAlchemy  │  │  Alembic    │  │  Models   │ │
   │  │  Database   │  │    ORM      │  │ Migrations  │  │           │ │
   │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘ │
   └─────────────────────────────────────────────────────────────────┘

Support
-------

* **Issues**: Report bugs and feature requests on `GitHub Issues <https://github.com/forkrul/day3-cve-feed/issues>`_
* **Documentation**: This documentation is available online and in the repository
* **API Documentation**: Interactive API docs available at ``/api/v1/docs`` when running the service

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
