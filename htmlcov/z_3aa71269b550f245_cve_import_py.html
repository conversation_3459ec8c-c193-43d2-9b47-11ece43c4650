<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src/cve_feed_service/cli/cve_import.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src/cve_feed_service/cli/cve_import.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">72 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">72<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">2<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_3aa71269b550f245___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_3aa71269b550f245_main_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 15:34 +0200
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""CLI command for CVE data import."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">asyncio</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Optional</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">structlog</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">typer</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">ext</span><span class="op">.</span><span class="nam">asyncio</span> <span class="key">import</span> <span class="nam">AsyncSession</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">config</span> <span class="key">import</span> <span class="nam">get_settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">AsyncSessionLocal</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">services</span><span class="op">.</span><span class="nam">cve_ingestion_service</span> <span class="key">import</span> <span class="nam">CVEIngestionService</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">structlog</span><span class="op">.</span><span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">settings</span> <span class="op">=</span> <span class="nam">get_settings</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="nam">app</span> <span class="op">=</span> <span class="nam">typer</span><span class="op">.</span><span class="nam">Typer</span><span class="op">(</span><span class="nam">help</span><span class="op">=</span><span class="str">"CVE data import commands"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="op">@</span><span class="nam">app</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="key">def</span> <span class="nam">bulk_import</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">years</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">typer</span><span class="op">.</span><span class="nam">Option</span><span class="op">(</span><span class="num">2</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">"Number of years to import"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">force</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">typer</span><span class="op">.</span><span class="nam">Option</span><span class="op">(</span><span class="key">False</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">"Force import even if data exists"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="str">"""Perform bulk import of CVE data from NVD."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">asyncio</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">_bulk_import</span><span class="op">(</span><span class="nam">years</span><span class="op">,</span> <span class="nam">force</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="op">@</span><span class="nam">app</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t"><span class="key">def</span> <span class="nam">incremental_update</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">hours</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">typer</span><span class="op">.</span><span class="nam">Option</span><span class="op">(</span><span class="num">24</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">"Number of hours to look back for updates"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="str">"""Perform incremental update of CVE data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">asyncio</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">_incremental_update</span><span class="op">(</span><span class="nam">hours</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t"><span class="op">@</span><span class="nam">app</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t"><span class="key">def</span> <span class="nam">import_single</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">cve_id</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">typer</span><span class="op">.</span><span class="nam">Argument</span><span class="op">(</span><span class="op">...</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">"CVE ID to import (e.g., CVE-2023-1234)"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t"><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="str">"""Import a single CVE by ID."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="nam">asyncio</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">_import_single</span><span class="op">(</span><span class="nam">cve_id</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t"><span class="key">async</span> <span class="key">def</span> <span class="nam">_bulk_import</span><span class="op">(</span><span class="nam">years</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">force</span><span class="op">:</span> <span class="nam">bool</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="str">"""Perform bulk import of CVE data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Starting bulk CVE import"</span><span class="op">,</span> <span class="nam">years</span><span class="op">=</span><span class="nam">years</span><span class="op">,</span> <span class="nam">force</span><span class="op">=</span><span class="nam">force</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">    <span class="key">async</span> <span class="key">with</span> <span class="nam">AsyncSessionLocal</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">            <span class="com"># Check if we already have recent CVE data</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">force</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">                <span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">cve</span> <span class="key">import</span> <span class="nam">CVE</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">                <span class="key">from</span> <span class="nam">sqlalchemy</span> <span class="key">import</span> <span class="nam">func</span><span class="op">,</span> <span class="nam">select</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">                <span class="nam">result</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">db</span><span class="op">.</span><span class="nam">execute</span><span class="op">(</span><span class="nam">select</span><span class="op">(</span><span class="nam">func</span><span class="op">.</span><span class="nam">count</span><span class="op">(</span><span class="nam">CVE</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">                <span class="nam">existing_count</span> <span class="op">=</span> <span class="nam">result</span><span class="op">.</span><span class="nam">scalar</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">                <span class="key">if</span> <span class="nam">existing_count</span> <span class="op">></span> <span class="num">1000</span><span class="op">:</span>  <span class="com"># Arbitrary threshold</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">                    <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                        <span class="str">"CVE data already exists. Use --force to reimport"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                        <span class="nam">existing_count</span><span class="op">=</span><span class="nam">existing_count</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                    <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="nam">service</span> <span class="op">=</span> <span class="nam">CVEIngestionService</span><span class="op">(</span><span class="nam">db</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="nam">processed_count</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">service</span><span class="op">.</span><span class="nam">perform_bulk_import</span><span class="op">(</span><span class="nam">years</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Bulk import completed"</span><span class="op">,</span> <span class="nam">processed_count</span><span class="op">=</span><span class="nam">processed_count</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Successfully imported {processed_count} CVEs"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Bulk import failed"</span><span class="op">,</span> <span class="nam">error</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Import failed: {e}"</span><span class="op">,</span> <span class="nam">err</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">            <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t"><span class="key">async</span> <span class="key">def</span> <span class="nam">_incremental_update</span><span class="op">(</span><span class="nam">hours</span><span class="op">:</span> <span class="nam">int</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="str">"""Perform incremental update of CVE data."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Starting incremental CVE update"</span><span class="op">,</span> <span class="nam">hours</span><span class="op">=</span><span class="nam">hours</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">    <span class="key">async</span> <span class="key">with</span> <span class="nam">AsyncSessionLocal</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="nam">service</span> <span class="op">=</span> <span class="nam">CVEIngestionService</span><span class="op">(</span><span class="nam">db</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">            <span class="nam">processed_count</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">service</span><span class="op">.</span><span class="nam">perform_incremental_update</span><span class="op">(</span><span class="nam">hours</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Incremental update completed"</span><span class="op">,</span> <span class="nam">processed_count</span><span class="op">=</span><span class="nam">processed_count</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">            <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Successfully updated {processed_count} CVEs"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Incremental update failed"</span><span class="op">,</span> <span class="nam">error</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">            <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Update failed: {e}"</span><span class="op">,</span> <span class="nam">err</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t"><span class="key">async</span> <span class="key">def</span> <span class="nam">_import_single</span><span class="op">(</span><span class="nam">cve_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="str">"""Import a single CVE by ID."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Importing single CVE"</span><span class="op">,</span> <span class="nam">cve_id</span><span class="op">=</span><span class="nam">cve_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="key">async</span> <span class="key">with</span> <span class="nam">AsyncSessionLocal</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">            <span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">services</span><span class="op">.</span><span class="nam">nvd_client</span> <span class="key">import</span> <span class="nam">NVDAPIClient</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">            <span class="key">async</span> <span class="key">with</span> <span class="nam">NVDAPIClient</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">                <span class="nam">cve_data</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">client</span><span class="op">.</span><span class="nam">get_cve_by_id</span><span class="op">(</span><span class="nam">cve_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">                <span class="key">if</span> <span class="key">not</span> <span class="nam">cve_data</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">                    <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"CVE {cve_id} not found"</span><span class="op">,</span> <span class="nam">err</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">                    <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">                <span class="nam">service</span> <span class="op">=</span> <span class="nam">CVEIngestionService</span><span class="op">(</span><span class="nam">db</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">                <span class="nam">cve</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">service</span><span class="op">.</span><span class="nam">ingest_cve</span><span class="op">(</span><span class="nam">cve_data</span><span class="op">[</span><span class="str">"cve"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">                <span class="key">await</span> <span class="nam">db</span><span class="op">.</span><span class="nam">commit</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">                <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"Single CVE import completed"</span><span class="op">,</span> <span class="nam">cve_id</span><span class="op">=</span><span class="nam">cve</span><span class="op">.</span><span class="nam">cve_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">                <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Successfully imported {cve.cve_id}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="str">"Single CVE import failed"</span><span class="op">,</span> <span class="nam">cve_id</span><span class="op">=</span><span class="nam">cve_id</span><span class="op">,</span> <span class="nam">error</span><span class="op">=</span><span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">            <span class="nam">typer</span><span class="op">.</span><span class="nam">echo</span><span class="op">(</span><span class="str">f"Import failed: {e}"</span><span class="op">,</span> <span class="nam">err</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">            <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">    <span class="nam">app</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_3aa71269b550f245___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_3aa71269b550f245_main_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 15:34 +0200
        </p>
    </div>
</footer>
</body>
</html>
