<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">41%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 15:34 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7___init___py.html">src/cve_feed_service/__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05fa98826f84bd0d___init___py.html">src/cve_feed_service/api/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9___init___py.html">src/cve_feed_service/api/v1/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9___init___py.html">src/cve_feed_service/api/v1/endpoints/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td>56</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="20 56">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td>95</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="33 95">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td>88</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="26 88">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td>42</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="16 42">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9_router_py.html">src/cve_feed_service/api/v1/router.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245___init___py.html">src/cve_feed_service/cli/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html">src/cve_feed_service/cli/cve_import.py</a></td>
                <td>72</td>
                <td>72</td>
                <td>2</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html">src/cve_feed_service/cli/main.py</a></td>
                <td>8</td>
                <td>8</td>
                <td>2</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f___init___py.html">src/cve_feed_service/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html">src/cve_feed_service/core/auth.py</a></td>
                <td>35</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="17 35">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html">src/cve_feed_service/core/config.py</a></td>
                <td>52</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="47 52">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html">src/cve_feed_service/core/dependencies.py</a></td>
                <td>55</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="20 55">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e___init___py.html">src/cve_feed_service/db/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html">src/cve_feed_service/db/base.py</a></td>
                <td>35</td>
                <td>4</td>
                <td>3</td>
                <td class="right" data-ratio="31 35">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html">src/cve_feed_service/db/database.py</a></td>
                <td>23</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="10 23">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html">src/cve_feed_service/main.py</a></td>
                <td>31</td>
                <td>6</td>
                <td>3</td>
                <td class="right" data-ratio="25 31">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5___init___py.html">src/cve_feed_service/models/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html">src/cve_feed_service/models/application.py</a></td>
                <td>35</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html">src/cve_feed_service/models/cve.py</a></td>
                <td>45</td>
                <td>2</td>
                <td>6</td>
                <td class="right" data-ratio="43 45">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html">src/cve_feed_service/models/user.py</a></td>
                <td>33</td>
                <td>2</td>
                <td>6</td>
                <td class="right" data-ratio="31 33">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f___init___py.html">src/cve_feed_service/schemas/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html">src/cve_feed_service/schemas/application.py</a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html">src/cve_feed_service/schemas/auth.py</a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html">src/cve_feed_service/schemas/cve.py</a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7___init___py.html">src/cve_feed_service/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html">src/cve_feed_service/services/application_service.py</a></td>
                <td>67</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="16 67">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html">src/cve_feed_service/services/auth_service.py</a></td>
                <td>86</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="22 86">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html">src/cve_feed_service/services/component_service.py</a></td>
                <td>111</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="21 111">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html">src/cve_feed_service/services/cve_service.py</a></td>
                <td>74</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="16 74">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html">src/cve_feed_service/services/nvd_client.py</a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b___init___py.html">src/cve_feed_service/utils/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td>65</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="21 65">32%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1557</td>
                <td>913</td>
                <td>31</td>
                <td class="right" data-ratio="644 1557">41%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 15:34 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_a5fe795f0915891b_cpe_utils_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_82e9ebae9fe787a7___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
